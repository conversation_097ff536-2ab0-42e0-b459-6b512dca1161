.presentation{
    background: url('background.jpg') no-repeat;
    font-family: 'Montserrat', sans-serif;
    width: 100%;
    height: 500px;
    background-size: cover;
    background-position: center center;
    background-blend-mode: multiply;
    background-color: #c0c0c0;
    position: relative;
    margin-bottom: 25px;
}

.presentation::after{
    content: '永恒的优雅为您呈现';
    position: absolute;
    top: 150px;
    left: 50px;
    width: 300px;
    font-size: 45px;
    font-weight: 600;
    color: #fff;
    line-height: 0.9;
}

.presentation::before{
    content: '我们创造条件，您享受舒适';
    position: absolute;
    top: 280px;
    left: 50px;
    width: 400px;
    font-size: 20px;
    font-weight: 500;
    color: #fff;
    line-height: 1.2;
}

.info-about{
    font-size: 17px;
}
.main-title{
    font-size: 20px;
    text-align: center;
}