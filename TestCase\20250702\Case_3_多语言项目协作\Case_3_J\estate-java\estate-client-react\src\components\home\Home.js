import React, {useState, useEffect} from 'react'
import {Statistic, Icon, Grid, Container, Image, Segment, Dimmer, Loader} from 'semantic-ui-react'
import {estateApi} from '../misc/EstateApi'
import {handleLogError} from '../misc/Helpers'
import "./Home.css"
import Footer from "../Footer";

function Home() {

    return (
        <Container>
            <div>
                <div className='presentation'></div>
                <div className='info-about'>
                <h1 id='main-title'>欢迎来到 Dixon Estate</h1>
                <p>我们正在朝着我们雄心勃勃的目标稳步前进。我们为已取得的成就感到自豪，
                    这些成就在房地产市场上引发了显著变化，使市场更加注重质量和可及性。</p>

                <h2>使命</h2>

                <p>公司的使命是为哈萨克斯坦建筑市场引入新的质量标准，
                    打造新一代住宅项目，旨在改变城市面貌，为市民提供良好的舒适度和安全性。

                    公司开发的项目和谐地结合了哈萨克斯坦人民体面生活的所有关键要素：
                    质量 - 舒适 - 美观 - 可及性。
                    <p/>
                    <h2>战略</h2>

                    <p>公司使命的所有组成部分都得到了周密的项目发展战略支持。DIXON ESTATE的战略
                    包括与合作银行的参与，该银行为客户提供特殊的信贷条件。舒适性和美观性由战略
                    海外合作伙伴提供保障：建筑和住宅规划顾问。质量由内部质量控制部门和国际项目
                    经理团队保证。</p>
                </p>
                </div>
                <div><Footer/></div>

            </div>
            {/*<Grid stackable columns={2}>*/}
            {/*  <Grid.Row>*/}
            {/*    <Grid.Column textAlign='center'>*/}
            {/*      <Segment color='purple'>*/}
            {/*        <Statistic>*/}
            {/*          <Statistic.Value><Icon name='user' color='grey' />{numberOfUsers}</Statistic.Value>*/}
            {/*          <Statistic.Label>Users</Statistic.Label>*/}
            {/*        </Statistic>*/}
            {/*      </Segment>*/}
            {/*    </Grid.Column>*/}
            {/*    <Grid.Column textAlign='center'>*/}
            {/*      <Segment color='purple'>*/}
            {/*        <Statistic>*/}
            {/*          <Statistic.Value><Icon name='laptop' color='grey' />{numberOfEstates}</Statistic.Value>*/}
            {/*          <Statistic.Label>Estates</Statistic.Label>*/}
            {/*        </Statistic>*/}
            {/*      </Segment>*/}
            {/*    </Grid.Column>*/}
            {/*  </Grid.Row>*/}
            {/*</Grid>*/}

            {/*<Image src='https://react.semantic-ui.com/images/wireframe/media-paragraph.png' style={{ marginTop: '2em' }} />*/}
            {/*<Image src='https://react.semantic-ui.com/images/wireframe/paragraph.png' style={{ marginTop: '2em' }} />*/}
        </Container>
    )
}

export default Home