import React, {useState} from 'react'
import {NavLink, Navigate} from 'react-router-dom'
import {Button, Form, Grid, Icon, Segment, Menu, Message, Divider} from 'semantic-ui-react'
import {useAuth} from '../context/AuthContext'
import {estateApi} from '../misc/EstateApi'
import {parseJwt, getSocialLoginUrl, handleLogError} from '../misc/Helpers'
import Footer from "../Footer";

function Login() {
    const Auth = useAuth()
    const isLoggedIn = Auth.userIsAuthenticated()

    const [username, setUsername] = useState('')
    const [password, setPassword] = useState('')
    const [isError, setIsError] = useState(false)

    const handleInputChange = (e, {name, value}) => {
        if (name === 'username') {
            setUsername(value)
        } else if (name === 'password') {
            setPassword(value)
        }
    }

    const handleSubmit = async (e) => {
        e.preventDefault()

        if (!(username && password)) {
            setIsError(true)
            return
        }

        try {
            const response = await estateApi.authenticate(username, password)
            const {accessToken} = response.data
            const data = parseJwt(accessToken)
            const authenticatedUser = {data, accessToken}

            Auth.userLogin(authenticatedUser)

            setUsername('')
            setPassword('')
            setIsError(false)
        } catch (error) {
            handleLogError(error)
            setIsError(true)
        }
    }

    if (isLoggedIn) {
        return <Navigate to='/'/>
    }

    return (
        <div>
            <Grid textAlign='center'>
                <Grid.Column style={{maxWidth: 450}}>
                    <Form size='large' onSubmit={handleSubmit}>
                        <Segment>
                            <Form.Input
                                fluid
                                autoFocus
                                name='username'
                                icon='user'
                                iconPosition='left'
                                placeholder='用户名'
                                onChange={handleInputChange}
                            />
                            <Form.Input
                                fluid
                                name='password'
                                icon='lock'
                                iconPosition='left'
                                placeholder='密码'
                                type='password'
                                onChange={handleInputChange}
                            />
                            <Button color='grey' fluid size='large'>登录</Button>
                        </Segment>
                    </Form>
                    <Message>{`还没有账号？ `}
                        <NavLink to="/signup" color='grey'>立即注册</NavLink>
                    </Message>
                    {isError && <Message negative>用户名或密码错误！</Message>}
                </Grid.Column>
            </Grid>
            <Footer/>
        </div>
    )
}

export default Login
