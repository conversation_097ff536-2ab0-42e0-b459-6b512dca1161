import React from 'react'
import { Form, Icon, Button } from 'semantic-ui-react'

function EstateForm({estateTitle, estatePoster, estateDescription, estateContact, estatePrice, estateAddress, handleInputChange, handleAddEstate }) {
  const createBtnDisabled = estateTitle.trim() === ''
  return (
      <Form onSubmit={handleAddEstate}>
        <Form.Field>
          <label>标题</label>
          <Form.Input
              name='estateTitle'
              placeholder='请输入标题'
              value={estateTitle}
              onChange={handleInputChange}
          />
        </Form.Field>
        <Form.Field>
          <label>图片链接</label>
          <Form.Input
              name='estatePoster'
              placeholder='请输入图片链接'
              value={estatePoster}
              onChange={handleInputChange}
          />
        </Form.Field>
        <Form.Field>
          <label>描述</label>
          <Form.Input
              name='estateDescription'
              placeholder='请输入描述'
              value={estateDescription}
              onChange={handleInputChange}
          />
        </Form.Field>
        <Form.Field>
          <label>联系方式</label>
          <Form.Input
              name='estateContact'
              placeholder='请输入联系方式'
              value={estateContact}
              onChange={handleInputChange}
          />
        </Form.Field>
        <Form.Field>
          <label>价格</label>
          <Form.Input
              name='estatePrice'
              placeholder='请输入每平方米价格'
              value={estatePrice}
              onChange={handleInputChange}
          />
        </Form.Field>
        <Form.Field>
          <label>地址</label>
          <Form.Input
              name='estateAddress'
              placeholder='请输入地址'
              value={estateAddress}
              onChange={handleInputChange}
          />
        </Form.Field>
        <Button icon labelPosition='right' disabled={createBtnDisabled}>
          创建<Icon name='add' />
        </Button>
      </Form>
  )
}

export default EstateForm
